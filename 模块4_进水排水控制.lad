// 模块4：进水排水控制
// S7-1215C 洗衣机控制系统 - 博图V18版本

ORGANIZATION_BLOCK "Module4_Water_Control" [OB4]
TITLE = 进水排水控制模块
VERSION : 0.1

VAR_TEMP
END_VAR

BEGIN

// Network 1: 高水位进水控制
NETWORK
TITLE = 高水位进水定时器
    "Start_Water_Fill"          "High_Water_Selected"           "Water_Inlet_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    开始进水                     高水位选择                      进水定时器
                                                                PT:=T#1m

// Network 2: 低水位进水控制
NETWORK
TITLE = 低水位进水定时器
    "Start_Water_Fill"          "Low_Water_Selected"            "Water_Inlet_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    开始进水                     低水位选择                      进水定时器
                                                                PT:=T#30s

// Network 3: 进水阀控制
NETWORK
TITLE = 进水阀控制
    "Start_Water_Fill"          "Water_Inlet_Timer".Q           "Water_Inlet_Valve"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    开始进水                     进水时间未到                    进水阀开启

// Network 4: 进水指示灯控制
NETWORK
TITLE = 进水指示灯控制
    "Water_Inlet_Valve"         "Water_Inlet_LED"
  ──┤ ├─────────────────────────( )──
    进水阀开启                   进水指示灯亮

    "Water_Inlet_Valve"         "Water_Drain_LED"
  ──┤ ├─────────────────────────( R )──
    进水阀开启                   排水指示灯熄灭

// Network 5: 进水完成检测
NETWORK
TITLE = 进水完成检测
    "Water_Inlet_Timer".Q       "Water_Fill_Complete"
  ──┤ ├─────────────────────────( S )──
    进水定时器到                 进水完成
                                "Start_Water_Fill"
                                ( R )──
                                开始进水复位
                                "Water_Inlet_Valve"
                                ( R )──
                                进水阀关闭
                                "Water_Inlet_LED"
                                ( R )──
                                进水指示灯熄灭

// Network 6: 洗涤后排水控制
NETWORK
TITLE = 洗涤完成后排水
    "Wash_Complete"             "Start_Drain_After_Wash"
  ──┤ ├─────────────────────────( S )──
    洗涤完成                     开始洗涤后排水
                                "Wash_Complete"
                                ( R )──
                                洗涤完成复位

// Network 7: 清洗后排水控制
NETWORK
TITLE = 清洗完成后排水
    "Rinse_Complete"            "Start_Drain_After_Rinse"
  ──┤ ├─────────────────────────( S )──
    清洗完成                     开始清洗后排水
                                "Rinse_Complete"
                                ( R )──
                                清洗完成复位

// Network 8: 脱水后排水控制
NETWORK
TITLE = 脱水完成后排水
    "Spin_Complete"             "Start_Drain_After_Spin"
  ──┤ ├─────────────────────────( S )──
    脱水完成                     开始脱水后排水
                                "Spin_Complete"
                                ( R )──
                                脱水完成复位

// Network 9: 高水位排水定时器
NETWORK
TITLE = 高水位排水定时器
    "Start_Drain_After_Wash"    "High_Water_Selected"           "Water_Drain_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    开始洗涤后排水               高水位选择                      排水定时器
                                                                PT:=T#1m

    "Start_Drain_After_Rinse"   "High_Water_Selected"           "Water_Drain_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    开始清洗后排水               高水位选择                      排水定时器
                                                                PT:=T#1m

    "Start_Drain_After_Spin"    "High_Water_Selected"           "Water_Drain_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    开始脱水后排水               高水位选择                      排水定时器
                                                                PT:=T#1m

// Network 10: 低水位排水定时器
NETWORK
TITLE = 低水位排水定时器
    "Start_Drain_After_Wash"    "Low_Water_Selected"            "Water_Drain_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    开始洗涤后排水               低水位选择                      排水定时器
                                                                PT:=T#30s

    "Start_Drain_After_Rinse"   "Low_Water_Selected"            "Water_Drain_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    开始清洗后排水               低水位选择                      排水定时器
                                                                PT:=T#30s

    "Start_Drain_After_Spin"    "Low_Water_Selected"            "Water_Drain_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    开始脱水后排水               低水位选择                      排水定时器
                                                                PT:=T#30s

// Network 11: 排水阀控制
NETWORK
TITLE = 排水阀控制
    "Start_Drain_After_Wash"    "Water_Drain_Timer".Q           "Water_Drain_Valve"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    开始洗涤后排水               排水时间未到                    排水阀开启

    "Start_Drain_After_Rinse"   "Water_Drain_Timer".Q           "Water_Drain_Valve"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    开始清洗后排水               排水时间未到                    排水阀开启

    "Start_Drain_After_Spin"    "Water_Drain_Timer".Q           "Water_Drain_Valve"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    开始脱水后排水               排水时间未到                    排水阀开启

// Network 12: 排水指示灯控制
NETWORK
TITLE = 排水指示灯控制
    "Water_Drain_Valve"         "Water_Drain_LED"
  ──┤ ├─────────────────────────( )──
    排水阀开启                   排水指示灯亮

    "Water_Drain_Valve"         "Water_Inlet_LED"
  ──┤ ├─────────────────────────( R )──
    排水阀开启                   进水指示灯熄灭

// Network 13: 洗涤后排水完成检测
NETWORK
TITLE = 洗涤后排水完成检测
    "Water_Drain_Timer".Q       "Start_Drain_After_Wash"        "Drain_Complete_After_Wash"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    排水定时器到                 洗涤后排水                      洗涤后排水完成
                                "Start_Drain_After_Wash"
                                ( R )──
                                洗涤后排水复位
                                "Water_Drain_Valve"
                                ( R )──
                                排水阀关闭
                                "Water_Drain_LED"
                                ( R )──
                                排水指示灯熄灭

// Network 14: 清洗后排水完成检测
NETWORK
TITLE = 清洗后排水完成检测
    "Water_Drain_Timer".Q       "Start_Drain_After_Rinse"       "Drain_Complete_After_Rinse"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    排水定时器到                 清洗后排水                      清洗后排水完成
                                "Start_Drain_After_Rinse"
                                ( R )──
                                清洗后排水复位
                                "Water_Drain_Valve"
                                ( R )──
                                排水阀关闭
                                "Water_Drain_LED"
                                ( R )──
                                排水指示灯熄灭

// Network 15: 脱水后排水完成检测
NETWORK
TITLE = 脱水后排水完成检测
    "Water_Drain_Timer".Q       "Start_Drain_After_Spin"        "Drain_Complete_After_Spin"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    排水定时器到                 脱水后排水                      脱水后排水完成
                                "Start_Drain_After_Spin"
                                ( R )──
                                脱水后排水复位
                                "Water_Drain_Valve"
                                ( R )──
                                排水阀关闭
                                "Water_Drain_LED"
                                ( R )──
                                排水指示灯熄灭
                                "Program_Complete"
                                ( S )──
                                程序完成

END_ORGANIZATION_BLOCK
