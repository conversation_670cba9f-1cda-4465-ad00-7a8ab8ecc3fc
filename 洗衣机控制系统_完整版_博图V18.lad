// S7-1215C 洗衣机控制系统 - 博图V18完整版
// 功能：洗涤/清洗/脱水，方式：标准/羊毛/强洗

ORGANIZATION_BLOCK "Main" [OB1]
TITLE = 洗衣机主控制程序
VERSION : 0.1

VAR_TEMP
END_VAR

BEGIN

// Network 1: 系统初始化
NETWORK
TITLE = 电源开关控制和默认设置
    "Power_Switch"              "System_Power_On"
  ──┤ ├─────────────────────────( )──
    电源开关                     系统通电

    "Power_Switch"              "Wash_Function"
  ──┤ ├─────────────────────────( S )──
    电源开关                     洗涤功能默认
                                "Rinse_Function"
                                ( R )──
                                清洗功能复位
                                "Spin_Function"
                                ( R )──
                                脱水功能复位

    "Power_Switch"              "Standard_Mode"
  ──┤ ├─────────────────────────( S )──
    电源开关                     标准模式默认
                                "Wool_Mode"
                                ( R )──
                                羊毛模式复位
                                "Heavy_Mode"
                                ( R )──
                                强洗模式复位

// Network 2: 按键边沿检测
NETWORK
TITLE = 功能按钮边沿检测
    "Function_Button"           "Function_Button_Last"          "Function_Edge"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    功能按钮                     上次状态取反                    功能切换边沿

    "Function_Button"           "Function_Button_Last"
  ──┤ ├─────────────────────────( )──
    功能按钮                     保存当前状态

NETWORK
TITLE = 程序按钮边沿检测
    "Program_Button"            "Program_Button_Last"           "Program_Edge"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    程序按钮                     上次状态取反                    程序切换边沿

    "Program_Button"            "Program_Button_Last"
  ──┤ ├─────────────────────────( )──
    程序按钮                     保存当前状态

NETWORK
TITLE = 启停按钮边沿检测
    "Start_Stop_Button"         "Start_Stop_Last"               "Start_Stop_Edge"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    启停按钮                     上次状态取反                    启停边沿

    "Start_Stop_Button"         "Start_Stop_Last"
  ──┤ ├─────────────────────────( )──
    启停按钮                     保存当前状态

// Network 3: 功能切换逻辑（洗涤→清洗→脱水→洗涤）
NETWORK
TITLE = 洗涤功能到清洗功能
    "Function_Edge"             "Wash_Function"                 "Rinse_Function"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    功能切换边沿                 洗涤功能                        清洗功能
                                "Wash_Function"
                                ( R )──
                                洗涤功能复位
                                "Spin_Function"
                                ( R )──
                                脱水功能复位

NETWORK
TITLE = 清洗功能到脱水功能
    "Function_Edge"             "Rinse_Function"                "Spin_Function"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    功能切换边沿                 清洗功能                        脱水功能
                                "Rinse_Function"
                                ( R )──
                                清洗功能复位

NETWORK
TITLE = 脱水功能到洗涤功能
    "Function_Edge"             "Spin_Function"                 "Wash_Function"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    功能切换边沿                 脱水功能                        洗涤功能
                                "Spin_Function"
                                ( R )──
                                脱水功能复位

// Network 4: 程序切换逻辑（标准→羊毛→强洗→标准）
NETWORK
TITLE = 标准模式到羊毛模式
    "Program_Edge"              "Standard_Mode"                 "Wool_Mode"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    程序切换边沿                 标准模式                        羊毛模式
                                "Standard_Mode"
                                ( R )──
                                标准模式复位
                                "Heavy_Mode"
                                ( R )──
                                强洗模式复位

NETWORK
TITLE = 羊毛模式到强洗模式
    "Program_Edge"              "Wool_Mode"                     "Heavy_Mode"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    程序切换边沿                 羊毛模式                        强洗模式
                                "Wool_Mode"
                                ( R )──
                                羊毛模式复位

NETWORK
TITLE = 强洗模式到标准模式
    "Program_Edge"              "Heavy_Mode"                    "Standard_Mode"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    程序切换边沿                 强洗模式                        标准模式
                                "Heavy_Mode"
                                ( R )──
                                强洗模式复位

// Network 5: 水位选择控制
NETWORK
TITLE = 高水位选择
    "High_Water_Switch"         "High_Water_Selected"
  ──┤ ├─────────────────────────( )──
    高水位开关                   高水位选择

    "High_Water_Switch"         "Low_Water_Selected"
  ──┤ ├─────────────────────────( R )──
    高水位开关                   低水位选择复位

NETWORK
TITLE = 低水位选择
    "Low_Water_Switch"          "Low_Water_Selected"
  ──┤ ├─────────────────────────( )──
    低水位开关                   低水位选择

    "Low_Water_Switch"          "High_Water_Selected"
  ──┤ ├─────────────────────────( R )──
    低水位开关                   高水位选择复位

// Network 6: 门盖检测
NETWORK
TITLE = 门盖状态检测
    "Door_Switch"               "Door_Closed"
  ──┤ ├─────────────────────────( )──
    门盖开关                     门盖关闭状态

// Network 7: 启动条件检查
NETWORK
TITLE = 系统启动条件
    "Start_Stop_Edge"           "Door_Closed"                   "System_Running"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    启停边沿                     门盖关闭                        系统运行
                                "System_Stopped"
                                ( R )──
                                系统停止复位

    "Start_Stop_Edge"           "System_Running"                "System_Stopped"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    启停边沿                     系统运行                        系统停止
                                "System_Running"
                                ( R )──
                                系统运行复位

// Network 8: 进水控制
NETWORK
TITLE = 高水位进水控制
    "System_Running"            "High_Water_Selected"           "Water_Inlet_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    系统运行                     高水位选择                      进水定时器
                                                                PT:=T#1m

    "System_Running"            "High_Water_Selected"           "Water_Inlet_Timer".Q           "Water_Inlet_Valve"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( )──
    系统运行                     高水位选择                      进水时间未到                    进水阀

NETWORK
TITLE = 低水位进水控制
    "System_Running"            "Low_Water_Selected"            "Water_Inlet_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    系统运行                     低水位选择                      进水定时器
                                                                PT:=T#30s

    "System_Running"            "Low_Water_Selected"            "Water_Inlet_Timer".Q           "Water_Inlet_Valve"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( )──
    系统运行                     低水位选择                      进水时间未到                    进水阀

// Network 9: 进水完成检测
NETWORK
TITLE = 进水完成检测
    "Water_Inlet_Timer".Q       "Water_Fill_Complete"
  ──┤ ├─────────────────────────( S )──
    进水定时器到                 进水完成
                                "Water_Inlet_Valve"
                                ( R )──
                                进水阀关闭

// Network 10: 洗涤程序启动
NETWORK
TITLE = 洗涤程序启动条件
    "Water_Fill_Complete"       "Wash_Function"                 "Wash_Process_Start"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    进水完成                     洗涤功能                        洗涤程序开始
                                "Water_Fill_Complete"
                                ( R )──
                                进水完成复位

    "Water_Fill_Complete"       "Rinse_Function"                "Rinse_Process_Start"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    进水完成                     清洗功能                        清洗程序开始
                                "Water_Fill_Complete"
                                ( R )──
                                进水完成复位

    "Water_Fill_Complete"       "Spin_Function"                 "Spin_Process_Start"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    进水完成                     脱水功能                        脱水程序开始
                                "Water_Fill_Complete"
                                ( R )──
                                进水完成复位

END_ORGANIZATION_BLOCK
