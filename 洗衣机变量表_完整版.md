# S7-1215C 洗衣机控制系统 - 完整变量表

## 输入变量表 (Digital Input)

| 地址 | 符号名称 | 中文描述 | 数据类型 | 备注 |
|------|----------|----------|----------|------|
| %I0.0 | Power_Switch | 电源开关 | BOOL | 常开触点，1=通电 |
| %I0.1 | Start_Pause | 启动/暂停开关 | BOOL | 薄膜开关，按下接通 |
| %I0.2 | Intensity_Select | 强度选择开关 | BOOL | 0=标准，1=柔和 |
| %I0.3 | Mode_Switch | 方式切换开关 | BOOL | 薄膜开关，按下接通 |
| %I0.4 | Door_Closed | 门盖检测开关 | BOOL | 门盖关闭时接通 |
| %I0.5 | Low_Water_Level | 低水位检测开关 | BOOL | 水位高于低水位时接通 |
| %I0.6 | High_Water_Level | 高水位检测开关 | BOOL | 水位高于高水位时接通 |
| %I0.7 | Emergency_Stop | 急停开关 | BOOL | 预留安全开关 |

## 输出变量表 (Digital Output)

### 执行器输出
| 地址 | 符号名称 | 中文描述 | 数据类型 | 备注 |
|------|----------|----------|----------|------|
| %Q0.0 | Water_Inlet_Valve | 进水阀 | BOOL | 1=开启进水 |
| %Q0.1 | Water_Drain_Valve | 排水阀 | BOOL | 1=开启排水 |
| %Q0.2 | Motor_Forward | 电机正转 | BOOL | 波轮正转 |
| %Q0.3 | Motor_Reverse | 电机反转 | BOOL | 波轮反转 |
| %Q0.4 | Spin_Clutch | 脱水电磁离合器 | BOOL | 1=脱水模式 |

### 指示灯输出
| 地址 | 符号名称 | 中文描述 | 数据类型 | 状态说明 |
|------|----------|----------|----------|----------|
| %Q1.0 | Wash_LED | 洗涤指示灯 | BOOL | 常亮=待洗涤，闪烁=洗涤中，熄灭=完成 |
| %Q1.1 | Rinse_LED | 清洗指示灯 | BOOL | 常亮=待清洗，闪烁=清洗中，熄灭=完成 |
| %Q1.2 | Spin_LED | 脱水指示灯 | BOOL | 常亮=待脱水，闪烁=脱水中，熄灭=完成 |
| %Q1.3 | Standard_LED | 标准强度指示灯 | BOOL | 选择标准强度时常亮 |
| %Q1.4 | Gentle_LED | 柔和强度指示灯 | BOOL | 选择柔和强度时常亮 |
| %Q1.7 | Error_LED | 故障指示灯 | BOOL | 系统故障时常亮 |

## 内部变量表 (Memory)

### 系统状态变量 (M0.x)
| 地址 | 符号名称 | 中文描述 | 数据类型 | 备注 |
|------|----------|----------|----------|------|
| %M0.0 | System_Ready | 系统准备就绪 | BOOL | 电源开启后置位 |
| %M0.1 | System_Running | 系统运行状态 | BOOL | 按启动后置位 |
| %M0.2 | System_Paused | 系统暂停状态 | BOOL | 运行中按暂停置位 |
| %M0.3 | Full_Program_Mode | 全程序模式 | BOOL | 洗涤→清洗→脱水 |
| %M0.4 | Rinse_Spin_Mode | 清洗脱水模式 | BOOL | 清洗→脱水 |
| %M0.5 | Spin_Only_Mode | 仅脱水模式 | BOOL | 仅脱水 |
| %M0.6 | Standard_Intensity | 标准强度选择 | BOOL | 标准洗涤强度 |
| %M0.7 | Gentle_Intensity | 柔和强度选择 | BOOL | 柔和洗涤强度 |

### 过程状态变量 (M1.x)
| 地址 | 符号名称 | 中文描述 | 数据类型 | 备注 |
|------|----------|----------|----------|------|
| %M1.0 | Water_Filling | 进水过程 | BOOL | 进水阀开启时置位 |
| %M1.1 | Washing | 洗涤过程 | BOOL | 洗涤进行时置位 |
| %M1.2 | Water_Draining | 排水过程 | BOOL | 排水阀开启时置位 |
| %M1.3 | Rinsing | 清洗过程 | BOOL | 清洗进行时置位 |
| %M1.4 | Spinning | 脱水过程 | BOOL | 脱水进行时置位 |
| %M1.5 | Process_Complete | 过程完成 | BOOL | 预留 |
| %M1.6 | Motor_Direction | 电机方向 | BOOL | 预留 |
| %M1.7 | Safety_OK | 安全条件满足 | BOOL | 预留 |

### 按键处理变量 (M2.x)
| 地址 | 符号名称 | 中文描述 | 数据类型 | 备注 |
|------|----------|----------|----------|------|
| %M2.0 | Start_Pause_Edge | 启动暂停按键边沿 | BOOL | 按键边沿检测 |
| %M2.1 | Mode_Switch_Edge | 模式切换按键边沿 | BOOL | 按键边沿检测 |
| %M2.2 | Intensity_Edge | 强度选择按键边沿 | BOOL | 按键边沿检测 |
| %M2.3 | Start_Pause_Last | 启动暂停按键上次状态 | BOOL | 边沿检测用 |
| %M2.4 | Mode_Switch_Last | 模式切换按键上次状态 | BOOL | 边沿检测用 |
| %M2.5 | Intensity_Last | 强度选择按键上次状态 | BOOL | 边沿检测用 |
| %M2.6 | Door_Safety | 门盖安全 | BOOL | 门盖关闭检测 |
| %M2.7 | Motor_Enable | 电机启动条件 | BOOL | 安全条件满足 |

### 工作流程变量 (M3.x)
| 地址 | 符号名称 | 中文描述 | 数据类型 | 备注 |
|------|----------|----------|----------|------|
| %M3.0 | Wash_Start | 洗涤开始 | BOOL | 洗涤流程启动 |
| %M3.1 | Wash_Complete | 洗涤完成 | BOOL | 洗涤流程完成 |
| %M3.2 | Rinse_Start | 清洗开始 | BOOL | 清洗流程启动 |
| %M3.3 | Rinse_Complete | 清洗完成 | BOOL | 清洗流程完成 |
| %M3.4 | Final_Spin_Start | 最终脱水开始 | BOOL | 最终脱水启动 |
| %M3.5 | Final_Spin_Complete | 最终脱水完成 | BOOL | 最终脱水完成 |
| %M3.6 | Intermediate_Spin | 中间脱水 | BOOL | 洗涤后中间脱水 |
| %M3.7 | Program_Complete | 程序完成 | BOOL | 整个程序完成 |

### 故障和保护变量 (M4.x)
| 地址 | 符号名称 | 中文描述 | 数据类型 | 备注 |
|------|----------|----------|----------|------|
| %M4.0 | Door_Error | 门盖故障 | BOOL | 门盖未关闭 |
| %M4.1 | Water_Level_Error | 水位故障 | BOOL | 水位传感器异常 |
| %M4.2 | Emergency_Stop_Active | 急停激活 | BOOL | 急停开关按下 |
| %M4.3 | System_Error | 系统故障 | BOOL | 综合故障状态 |

## 定时器变量表 (Timer)

| 定时器 | 符号名称 | 中文描述 | 时间设定 | 用途 |
|--------|----------|----------|----------|------|
| %T1 | Motor_Forward_Timer | 电机正转定时器 | 标准4s/柔和2s | 控制电机正转时间 |
| %T2 | Motor_Pause_Timer | 电机暂停定时器 | 1s | 控制电机暂停时间 |
| %T3 | Motor_Reverse_Timer | 电机反转定时器 | 标准4s/柔和2s | 控制电机反转时间 |
| %T4 | Motor_Pause2_Timer | 电机暂停定时器2 | 1s | 控制电机第二次暂停 |
| %T5 | Spin_Timer | 脱水定时器 | 标准20s/柔和10s | 控制脱水时间 |
| %T6 | Drain_Delay_Timer | 排水延时定时器 | 5s | 排水后延时 |
| %T7 | LED_Flash_Timer | 指示灯闪烁定时器 | 0.5s | 指示灯闪烁控制 |
| %T8 | Debounce_Timer | 按键防抖定时器 | 0.1s | 按键防抖处理 |

## 计数器变量表 (Counter)

| 计数器 | 符号名称 | 中文描述 | 设定值 | 用途 |
|--------|----------|----------|--------|------|
| %C1 | Wash_Cycle_Counter | 洗涤循环计数器 | 标准6次/柔和3次 | 洗涤循环计数 |
| %C2 | Rinse_Cycle_Counter | 清洗循环计数器 | 标准3次/柔和2次 | 清洗循环计数 |

## 程序控制逻辑说明

### 工作模式切换逻辑
1. **默认模式**：系统上电后默认为全程序模式(%M0.3)和标准强度(%M0.6)
2. **模式切换**：按模式切换开关循环切换：全程序→清洗脱水→仅脱水→全程序
3. **强度切换**：按强度选择开关在标准和柔和之间切换

### 安全保护逻辑
1. **门盖保护**：%M2.6 = %I0.4，门盖必须关闭
2. **电机启动条件**：%M2.7 = %M2.6 AND %M0.1，门盖关闭且系统运行
3. **故障综合**：%M4.3 = %M4.0 OR %M4.1 OR %M4.2

### 流程控制逻辑
1. **全程序模式**：进水→洗涤→排水→中间脱水→进水→清洗→排水→最终脱水
2. **清洗脱水模式**：进水→清洗→排水→最终脱水
3. **仅脱水模式**：直接最终脱水

### 指示灯控制逻辑
1. **常亮**：该过程在程序中但尚未开始
2. **闪烁**：该过程正在进行中（0.5s周期）
3. **熄灭**：该过程已完成或不在程序中
