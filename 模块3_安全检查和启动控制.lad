// 模块3：安全检查和启动控制
// S7-1215C 洗衣机控制系统 - 博图V18版本

ORGANIZATION_BLOCK "Module3_Safety_Start_Control" [OB3]
TITLE = 安全检查和启动控制模块
VERSION : 0.1

VAR_TEMP
END_VAR

BEGIN

// Network 1: 启动条件检查
NETWORK
TITLE = 系统启动条件检查
    "Start_Stop_Edge"           "Door_Switch"                   "System_Power_On"               "System_Running"                "System_Running"
  ──┤ ├─────────────────┤ ├─────────────────────┤ ├─────────────────────┤/├─────────────────────────────────( S )──
    启停按钮边沿                 门盖关闭                        系统通电                        系统未运行                      系统运行
                                "System_Stopped"
                                ( R )──
                                系统停止复位

// Network 2: 系统停止条件
NETWORK
TITLE = 系统停止条件
    "Start_Stop_Edge"           "System_Running"                "System_Stopped"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    启停按钮边沿                 系统运行中                      系统停止
                                "System_Running"
                                ( R )──
                                系统运行复位

// Network 3: 门盖安全检查
NETWORK
TITLE = 门盖安全检查 - 运行中门盖打开停止系统
    "System_Running"            "Door_Switch"                   "System_Stopped"
  ──┤ ├─────────────────┤/├─────────────────────────────────( S )──
    系统运行中                   门盖打开                        系统停止
                                "System_Running"
                                ( R )──
                                系统运行复位

// Network 4: 水位选择检查
NETWORK
TITLE = 水位选择检查
    "High_Water_Selected"       "Low_Water_Selected"            "Water_Level_Selected"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( )──
    高水位选择                   低水位选择                      水位已选择

// Network 5: 功能选择检查
NETWORK
TITLE = 功能选择检查
    "Wash_Function"             "Rinse_Function"                "Spin_Function"                 "Function_Selected"
  ──┤ ├─────────────────┤ ├─────────────────────┤ ├─────────────────────────────────( )──
    洗涤功能                     清洗功能                        脱水功能                        功能已选择

// Network 6: 程序选择检查
NETWORK
TITLE = 程序选择检查
    "Standard_Mode"             "Wool_Mode"                     "Heavy_Mode"                    "Program_Selected"
  ──┤ ├─────────────────┤ ├─────────────────────┤ ├─────────────────────────────────( )──
    标准模式                     羊毛模式                        强洗模式                        程序已选择

// Network 7: 系统准备就绪检查
NETWORK
TITLE = 系统准备就绪检查
    "System_Power_On"           "Door_Switch"                   "Water_Level_Selected"          "Function_Selected"             "Program_Selected"              "System_Ready"
  ──┤ ├─────────────────┤ ├─────────────────────┤ ├─────────────────────┤ ├─────────────────────┤ ├─────────────────────────────────( )──
    系统通电                     门盖关闭                        水位已选择                      功能已选择                      程序已选择                      系统准备就绪

// Network 8: 启动进水条件
NETWORK
TITLE = 启动进水条件
    "System_Running"            "System_Ready"                  "Water_Fill_Complete"           "Start_Water_Fill"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( )──
    系统运行                     系统准备就绪                    进水未完成                      开始进水

// Network 9: 系统停止时复位所有过程
NETWORK
TITLE = 系统停止时复位所有过程
    "System_Stopped"            "Wash_Process_Active"
  ──┤ ├─────────────────────────( R )──
    系统停止                     洗涤过程复位

    "System_Stopped"            "Rinse_Process_Active"
  ──┤ ├─────────────────────────( R )──
    系统停止                     清洗过程复位

    "System_Stopped"            "Spin_Process_Active"
  ──┤ ├─────────────────────────( R )──
    系统停止                     脱水过程复位

    "System_Stopped"            "Water_Fill_Complete"
  ──┤ ├─────────────────────────( R )──
    系统停止                     进水完成复位

    "System_Stopped"            "Wash_Complete"
  ──┤ ├─────────────────────────( R )──
    系统停止                     洗涤完成复位

    "System_Stopped"            "Rinse_Complete"
  ──┤ ├─────────────────────────( R )──
    系统停止                     清洗完成复位

    "System_Stopped"            "Spin_Complete"
  ──┤ ├─────────────────────────( R )──
    系统停止                     脱水完成复位

    "System_Stopped"            "Program_Complete"
  ──┤ ├─────────────────────────( R )──
    系统停止                     程序完成复位

// Network 10: 系统停止时关闭所有执行器
NETWORK
TITLE = 系统停止时关闭所有执行器
    "System_Stopped"            "Water_Inlet_Valve"
  ──┤ ├─────────────────────────( R )──
    系统停止                     进水阀关闭

    "System_Stopped"            "Water_Drain_Valve"
  ──┤ ├─────────────────────────( R )──
    系统停止                     排水阀关闭

    "System_Stopped"            "Motor_Forward"
  ──┤ ├─────────────────────────( R )──
    系统停止                     电机正转停止

    "System_Stopped"            "Motor_Reverse"
  ──┤ ├─────────────────────────( R )──
    系统停止                     电机反转停止

// Network 11: 系统停止时复位所有定时器
NETWORK
TITLE = 系统停止时复位所有定时器
    "System_Stopped"            "Water_Inlet_Timer"
  ──┤ ├─────────────────────────( R )──
    系统停止                     进水定时器复位

    "System_Stopped"            "Water_Drain_Timer"
  ──┤ ├─────────────────────────( R )──
    系统停止                     排水定时器复位

    "System_Stopped"            "Motor_Forward_Timer"
  ──┤ ├─────────────────────────( R )──
    系统停止                     正转定时器复位

    "System_Stopped"            "Motor_Pause_Timer"
  ──┤ ├─────────────────────────( R )──
    系统停止                     暂停定时器复位

    "System_Stopped"            "Motor_Reverse_Timer"
  ──┤ ├─────────────────────────( R )──
    系统停止                     反转定时器复位

    "System_Stopped"            "Motor_Pause2_Timer"
  ──┤ ├─────────────────────────( R )──
    系统停止                     暂停定时器2复位

    "System_Stopped"            "Wash_Program_Timer"
  ──┤ ├─────────────────────────( R )──
    系统停止                     洗涤程序定时器复位

    "System_Stopped"            "Rinse_Program_Timer"
  ──┤ ├─────────────────────────( R )──
    系统停止                     清洗程序定时器复位

    "System_Stopped"            "Spin_Program_Timer"
  ──┤ ├─────────────────────────( R )──
    系统停止                     脱水程序定时器复位

// Network 12: 系统停止时关闭状态指示灯
NETWORK
TITLE = 系统停止时关闭状态指示灯
    "System_Stopped"            "Water_Inlet_LED"
  ──┤ ├─────────────────────────( R )──
    系统停止                     进水指示灯熄灭

    "System_Stopped"            "Water_Drain_LED"
  ──┤ ├─────────────────────────( R )──
    系统停止                     排水指示灯熄灭

    "System_Stopped"            "Motor_Forward_LED"
  ──┤ ├─────────────────────────( R )──
    系统停止                     正转指示灯熄灭

    "System_Stopped"            "Motor_Reverse_LED"
  ──┤ ├─────────────────────────( R )──
    系统停止                     反转指示灯熄灭

END_ORGANIZATION_BLOCK
