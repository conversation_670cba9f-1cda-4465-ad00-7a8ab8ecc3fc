// 模块1：系统初始化和按键处理
// S7-1215C 洗衣机控制系统 - 博图V18版本

ORGANIZATION_BLOCK "Module1_System_Init" [OB1]
TITLE = 系统初始化和按键处理模块
VERSION : 0.1

VAR_TEMP
END_VAR

BEGIN

// Network 1: 电源开关控制
NETWORK
TITLE = 电源开关控制
    "Power_Switch"              "System_Power_On"
  ──┤ ├─────────────────────────( )──
    电源开关                     系统通电状态

// Network 2: 系统默认设置
NETWORK
TITLE = 电源开启时默认设置
    "Power_Switch"              "System_Power_On"               "Wash_Function"
  ──┤ ├─────────────────┤/├─────────────────────────────────( S )──
    电源开关                     系统未通电时                    洗涤功能默认
                                "Rinse_Function"
                                ( R )──
                                清洗功能复位
                                "Spin_Function"
                                ( R )──
                                脱水功能复位

    "Power_Switch"              "System_Power_On"               "Standard_Mode"
  ──┤ ├─────────────────┤/├─────────────────────────────────( S )──
    电源开关                     系统未通电时                    标准模式默认
                                "Wool_Mode"
                                ( R )──
                                羊毛模式复位
                                "Heavy_Mode"
                                ( R )──
                                强洗模式复位

// Network 3: 功能按钮边沿检测
NETWORK
TITLE = 功能按钮边沿检测
    "Function_Button"           "Function_Button_Last"          "Function_Edge"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    功能按钮当前状态             功能按钮上次状态取反             功能切换边沿

    "Function_Button"           "Function_Button_Last"
  ──┤ ├─────────────────────────( )──
    功能按钮当前状态             保存当前状态为上次状态

// Network 4: 程序按钮边沿检测
NETWORK
TITLE = 程序按钮边沿检测
    "Program_Button"            "Program_Button_Last"           "Program_Edge"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    程序按钮当前状态             程序按钮上次状态取反             程序切换边沿

    "Program_Button"            "Program_Button_Last"
  ──┤ ├─────────────────────────( )──
    程序按钮当前状态             保存当前状态为上次状态

// Network 5: 启停按钮边沿检测
NETWORK
TITLE = 启停按钮边沿检测
    "Start_Stop_Button"         "Start_Stop_Last"               "Start_Stop_Edge"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    启停按钮当前状态             启停按钮上次状态取反             启停切换边沿

    "Start_Stop_Button"         "Start_Stop_Last"
  ──┤ ├─────────────────────────( )──
    启停按钮当前状态             保存当前状态为上次状态

// Network 6: 电源关闭时系统复位
NETWORK
TITLE = 电源关闭时系统复位
    "Power_Switch"              "System_Running"
  ──┤/├─────────────────────────( R )──
    电源关闭                     系统运行复位

    "Power_Switch"              "System_Stopped"
  ──┤/├─────────────────────────( R )──
    电源关闭                     系统停止复位

    "Power_Switch"              "Wash_Process_Active"
  ──┤/├─────────────────────────( R )──
    电源关闭                     洗涤过程复位

    "Power_Switch"              "Rinse_Process_Active"
  ──┤/├─────────────────────────( R )──
    电源关闭                     清洗过程复位

    "Power_Switch"              "Spin_Process_Active"
  ──┤/├─────────────────────────( R )──
    电源关闭                     脱水过程复位

    "Power_Switch"              "Water_Fill_Complete"
  ──┤/├─────────────────────────( R )──
    电源关闭                     进水完成复位

    "Power_Switch"              "Wash_Complete"
  ──┤/├─────────────────────────( R )──
    电源关闭                     洗涤完成复位

    "Power_Switch"              "Rinse_Complete"
  ──┤/├─────────────────────────( R )──
    电源关闭                     清洗完成复位

    "Power_Switch"              "Spin_Complete"
  ──┤/├─────────────────────────( R )──
    电源关闭                     脱水完成复位

    "Power_Switch"              "Program_Complete"
  ──┤/├─────────────────────────( R )──
    电源关闭                     程序完成复位

// Network 7: 电源关闭时所有输出复位
NETWORK
TITLE = 电源关闭时所有输出复位
    "Power_Switch"              "Water_Inlet_Valve"
  ──┤/├─────────────────────────( R )──
    电源关闭                     进水阀关闭

    "Power_Switch"              "Water_Drain_Valve"
  ──┤/├─────────────────────────( R )──
    电源关闭                     排水阀关闭

    "Power_Switch"              "Motor_Forward"
  ──┤/├─────────────────────────( R )──
    电源关闭                     电机正转停止

    "Power_Switch"              "Motor_Reverse"
  ──┤/├─────────────────────────( R )──
    电源关闭                     电机反转停止

// Network 8: 电源关闭时所有指示灯复位
NETWORK
TITLE = 电源关闭时功能指示灯复位
    "Power_Switch"              "Wash_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     洗涤指示灯熄灭

    "Power_Switch"              "Rinse_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     清洗指示灯熄灭

    "Power_Switch"              "Spin_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     脱水指示灯熄灭

    "Power_Switch"              "Standard_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     标准模式指示灯熄灭

    "Power_Switch"              "Wool_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     羊毛模式指示灯熄灭

    "Power_Switch"              "Heavy_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     强洗模式指示灯熄灭

NETWORK
TITLE = 电源关闭时状态指示灯复位
    "Power_Switch"              "Door_Closed_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     门盖指示灯熄灭

    "Power_Switch"              "High_Water_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     高水位指示灯熄灭

    "Power_Switch"              "Low_Water_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     低水位指示灯熄灭

    "Power_Switch"              "Water_Inlet_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     进水指示灯熄灭

    "Power_Switch"              "Water_Drain_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     排水指示灯熄灭

    "Power_Switch"              "Motor_Forward_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     正转指示灯熄灭

    "Power_Switch"              "Motor_Reverse_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     反转指示灯熄灭

// Network 9: 电源关闭时定时器复位
NETWORK
TITLE = 电源关闭时定时器复位
    "Power_Switch"              "Water_Inlet_Timer"
  ──┤/├─────────────────────────( R )──
    电源关闭                     进水定时器复位

    "Power_Switch"              "Water_Drain_Timer"
  ──┤/├─────────────────────────( R )──
    电源关闭                     排水定时器复位

    "Power_Switch"              "Motor_Forward_Timer"
  ──┤/├─────────────────────────( R )──
    电源关闭                     正转定时器复位

    "Power_Switch"              "Motor_Pause_Timer"
  ──┤/├─────────────────────────( R )──
    电源关闭                     暂停定时器复位

    "Power_Switch"              "Motor_Reverse_Timer"
  ──┤/├─────────────────────────( R )──
    电源关闭                     反转定时器复位

    "Power_Switch"              "Motor_Pause2_Timer"
  ──┤/├─────────────────────────( R )──
    电源关闭                     暂停定时器2复位

    "Power_Switch"              "Wash_Program_Timer"
  ──┤/├─────────────────────────( R )──
    电源关闭                     洗涤程序定时器复位

    "Power_Switch"              "Rinse_Program_Timer"
  ──┤/├─────────────────────────( R )──
    电源关闭                     清洗程序定时器复位

    "Power_Switch"              "Spin_Program_Timer"
  ──┤/├─────────────────────────( R )──
    电源关闭                     脱水程序定时器复位

END_ORGANIZATION_BLOCK
