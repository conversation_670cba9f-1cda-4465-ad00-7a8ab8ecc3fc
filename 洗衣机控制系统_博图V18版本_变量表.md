# S7-1215C 洗衣机控制系统 - 博图V18版本变量表

## 符号表 (Symbol Table) - 符合TIA Portal V18规范

### 输入变量 (Input Variables)

| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| Power_Switch | %I0.0 | BOOL | 电源开关 | 常开触点，1=通电 |
| Start_Pause | %I0.1 | BOOL | 启动/暂停开关 | 薄膜开关，按下接通 |
| Intensity_Select | %I0.2 | BOOL | 强度选择开关 | 0=标准，1=柔和 |
| Mode_Switch | %I0.3 | BOOL | 方式切换开关 | 薄膜开关，按下接通 |
| Door_Closed | %I0.4 | BOOL | 门盖检测开关 | 门盖关闭时接通 |
| Low_Water_Level | %I0.5 | BOOL | 低水位检测开关 | 水位高于低水位时接通 |
| High_Water_Level | %I0.6 | BOOL | 高水位检测开关 | 水位高于高水位时接通 |
| Emergency_Stop | %I0.7 | BOOL | 急停开关 | 预留安全开关 |

### 输出变量 (Output Variables)

#### 执行器输出
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| Water_Inlet_Valve | %Q0.0 | BOOL | 进水阀 | 1=开启进水 |
| Water_Drain_Valve | %Q0.1 | BOOL | 排水阀 | 1=开启排水 |
| Motor_Forward | %Q0.2 | BOOL | 电机正转 | 波轮正转 |
| Motor_Reverse | %Q0.3 | BOOL | 电机反转 | 波轮反转 |
| Spin_Clutch | %Q0.4 | BOOL | 脱水电磁离合器 | 1=脱水模式 |

#### 指示灯输出
| 符号名称 | 地址 | 数据类型 | 中文描述 | 状态说明 |
|----------|------|----------|----------|----------|
| Wash_LED | %Q1.0 | BOOL | 洗涤指示灯 | 常亮=待洗涤，闪烁=洗涤中，熄灭=完成 |
| Rinse_LED | %Q1.1 | BOOL | 清洗指示灯 | 常亮=待清洗，闪烁=清洗中，熄灭=完成 |
| Spin_LED | %Q1.2 | BOOL | 脱水指示灯 | 常亮=待脱水，闪烁=脱水中，熄灭=完成 |
| Standard_LED | %Q1.3 | BOOL | 标准强度指示灯 | 选择标准强度时常亮 |
| Gentle_LED | %Q1.4 | BOOL | 柔和强度指示灯 | 选择柔和强度时常亮 |
| Error_LED | %Q1.7 | BOOL | 故障指示灯 | 系统故障时常亮 |

### 内部变量 (Memory Variables)

#### 系统状态变量
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| System_Ready | %M0.0 | BOOL | 系统准备就绪 | 电源开启后置位 |
| System_Running | %M0.1 | BOOL | 系统运行状态 | 按启动后置位 |
| System_Paused | %M0.2 | BOOL | 系统暂停状态 | 运行中按暂停置位 |
| Full_Program_Mode | %M0.3 | BOOL | 全程序模式 | 洗涤→清洗→脱水 |
| Rinse_Spin_Mode | %M0.4 | BOOL | 清洗脱水模式 | 清洗→脱水 |
| Spin_Only_Mode | %M0.5 | BOOL | 仅脱水模式 | 仅脱水 |
| Standard_Intensity | %M0.6 | BOOL | 标准强度选择 | 标准洗涤强度 |
| Gentle_Intensity | %M0.7 | BOOL | 柔和强度选择 | 柔和洗涤强度 |

#### 过程状态变量
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| Water_Filling | %M1.0 | BOOL | 进水过程 | 进水阀开启时置位 |
| Washing | %M1.1 | BOOL | 洗涤过程 | 洗涤进行时置位 |
| Water_Draining | %M1.2 | BOOL | 排水过程 | 排水阀开启时置位 |
| Rinsing | %M1.3 | BOOL | 清洗过程 | 清洗进行时置位 |
| Spinning | %M1.4 | BOOL | 脱水过程 | 脱水进行时置位 |

#### 按键处理变量
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| Start_Pause_Edge | %M2.0 | BOOL | 启动暂停按键边沿 | 按键边沿检测 |
| Mode_Switch_Edge | %M2.1 | BOOL | 模式切换按键边沿 | 按键边沿检测 |
| Intensity_Edge | %M2.2 | BOOL | 强度选择按键边沿 | 按键边沿检测 |
| Start_Pause_Last | %M2.3 | BOOL | 启动暂停按键上次状态 | 边沿检测用 |
| Mode_Switch_Last | %M2.4 | BOOL | 模式切换按键上次状态 | 边沿检测用 |
| Intensity_Last | %M2.5 | BOOL | 强度选择按键上次状态 | 边沿检测用 |
| Door_Safety | %M2.6 | BOOL | 门盖安全 | 门盖关闭检测 |
| Motor_Enable | %M2.7 | BOOL | 电机启动条件 | 安全条件满足 |

#### 工作流程变量
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| Wash_Start | %M3.0 | BOOL | 洗涤开始 | 洗涤流程启动 |
| Wash_Complete | %M3.1 | BOOL | 洗涤完成 | 洗涤流程完成 |
| Rinse_Start | %M3.2 | BOOL | 清洗开始 | 清洗流程启动 |
| Rinse_Complete | %M3.3 | BOOL | 清洗完成 | 清洗流程完成 |
| Final_Spin_Start | %M3.4 | BOOL | 最终脱水开始 | 最终脱水启动 |
| Final_Spin_Complete | %M3.5 | BOOL | 最终脱水完成 | 最终脱水完成 |
| Intermediate_Spin | %M3.6 | BOOL | 中间脱水 | 洗涤后中间脱水 |
| Program_Complete | %M3.7 | BOOL | 程序完成 | 整个程序完成 |

#### 故障和保护变量
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| Door_Error | %M4.0 | BOOL | 门盖故障 | 门盖未关闭 |
| Water_Level_Error | %M4.1 | BOOL | 水位故障 | 水位传感器异常 |
| Emergency_Stop_Active | %M4.2 | BOOL | 急停激活 | 急停开关按下 |
| System_Error | %M4.3 | BOOL | 系统故障 | 综合故障状态 |

### 定时器变量 (Timer Variables)

| 符号名称 | 地址 | 数据类型 | 中文描述 | 时间设定 | 用途 |
|----------|------|----------|----------|----------|------|
| Motor_Forward_Timer | %T1 | TON | 电机正转定时器 | 标准4s/柔和2s | 控制电机正转时间 |
| Motor_Pause_Timer | %T2 | TON | 电机暂停定时器 | 1s | 控制电机暂停时间 |
| Motor_Reverse_Timer | %T3 | TON | 电机反转定时器 | 标准4s/柔和2s | 控制电机反转时间 |
| Motor_Pause2_Timer | %T4 | TON | 电机暂停定时器2 | 1s | 控制电机第二次暂停 |
| Spin_Timer | %T5 | TON | 脱水定时器 | 标准20s/柔和10s | 控制脱水时间 |
| Drain_Delay_Timer | %T6 | TON | 排水延时定时器 | 5s | 排水后延时 |
| LED_Flash_Timer | %T7 | TON | 指示灯闪烁定时器 | 500ms | 指示灯闪烁控制 |

### 计数器变量 (Counter Variables)

| 符号名称 | 地址 | 数据类型 | 中文描述 | 设定值 | 用途 |
|----------|------|----------|----------|--------|------|
| Wash_Cycle_Counter | %C1 | CTU | 洗涤循环计数器 | 标准6次/柔和3次 | 洗涤循环计数 |
| Rinse_Cycle_Counter | %C2 | CTU | 清洗循环计数器 | 标准3次/柔和2次 | 清洗循环计数 |

## 博图V18语法特点

### 1. 符号命名规范
- 使用英文符号名称，便于国际化
- 采用下划线分隔单词
- 符号名称具有描述性

### 2. 定时器语法
```
"Motor_Forward_Timer".Q     // 定时器输出位
PT:=T#4s                    // 预设时间值
```

### 3. 计数器语法
```
"Wash_Cycle_Counter"        // 计数器名称
(CTU)                       // 上升沿计数器
PV:=6                       // 预设值
```

### 4. 置位复位语法
```
"Standard_Intensity"
( S )──                     // 置位指令
"Gentle_Intensity"
( R )──                     // 复位指令
```

### 5. 常闭触点语法
```
"Motor_Forward_Timer".Q     // 常闭触点
┤/├
```

这个博图V18版本的洗衣机控制系统完全符合TIA Portal V18的语法规范，使用了标准的符号表定义和梯形图语法。
