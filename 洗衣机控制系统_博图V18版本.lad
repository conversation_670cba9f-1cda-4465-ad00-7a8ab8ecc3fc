// S7-1215C 洗衣机控制系统 - 博图V18版本
// 符合TIA Portal V18语法规范

ORGANIZATION_BLOCK "Main" [OB1]
TITLE = 洗衣机主控制程序
VERSION : 0.1

VAR_TEMP
END_VAR

BEGIN

// Network 1: 系统初始化
NETWORK
TITLE = 系统初始化和电源控制
    "Power_Switch"              "System_Ready"
  ──┤ ├─────────────────────────( )──
    电源开关                     系统准备就绪

    "Power_Switch"              "Standard_Intensity"
  ──┤ ├─────────────────────────( S )──
    电源开关                     标准强度默认
                                "Gentle_Intensity"
                                ( R )──
                                柔和强度复位

    "Power_Switch"              "Full_Program_Mode"
  ──┤ ├─────────────────────────( S )──
    电源开关                     全程序模式默认
                                "Rinse_Spin_Mode"
                                ( R )──
                                清洗脱水模式复位
                                "Spin_Only_Mode"
                                ( R )──
                                仅脱水模式复位

// Network 2: 按键边沿检测
NETWORK
TITLE = 启动暂停按键边沿检测
    "Start_Pause"               "Start_Pause_Last"              "Start_Pause_Edge"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    启动暂停按键          上次状态取反                        启动暂停边沿

    "Start_Pause"               "Start_Pause_Last"
  ──┤ ├─────────────────────────( )──
    启动暂停按键                 保存当前状态

NETWORK
TITLE = 模式切换按键边沿检测
    "Mode_Switch"               "Mode_Switch_Last"              "Mode_Switch_Edge"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    模式切换按键                 上次状态取反                    模式切换边沿

    "Mode_Switch"               "Mode_Switch_Last"
  ──┤ ├─────────────────────────( )──
    模式切换按键                 保存当前状态

NETWORK
TITLE = 强度选择按键边沿检测
    "Intensity_Select"          "Intensity_Last"                "Intensity_Edge"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    强度选择按键                 上次状态取反                    强度选择边沿

    "Intensity_Select"          "Intensity_Last"
  ──┤ ├─────────────────────────( )──
    强度选择按键                 保存当前状态

// Network 3: 安全条件检测
NETWORK
TITLE = 门盖安全检测
    "Door_Closed"               "Door_Safety"
  ──┤ ├─────────────────────────( )──
    门盖关闭检测                 门盖安全

NETWORK
TITLE = 电机启动条件
    "Door_Safety"               "System_Running"                "Motor_Enable"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( )──
    门盖安全                     系统运行                        电机启动条件

// Network 4: 模式切换逻辑
NETWORK
TITLE = 全程序模式到清洗脱水模式
    "Mode_Switch_Edge"          "Full_Program_Mode"             "Rinse_Spin_Mode"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    模式切换边沿                 全程序模式                      清洗脱水模式
                                "Full_Program_Mode"
                                ( R )──
                                全程序模式复位
                                "Spin_Only_Mode"
                                ( R )──
                                仅脱水模式复位

NETWORK
TITLE = 清洗脱水模式到仅脱水模式
    "Mode_Switch_Edge"          "Rinse_Spin_Mode"               "Spin_Only_Mode"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    模式切换边沿                 清洗脱水模式                    仅脱水模式
                                "Rinse_Spin_Mode"
                                ( R )──
                                清洗脱水模式复位

NETWORK
TITLE = 仅脱水模式到全程序模式
    "Mode_Switch_Edge"          "Spin_Only_Mode"                "Full_Program_Mode"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    模式切换边沿                 仅脱水模式                      全程序模式
                                "Spin_Only_Mode"
                                ( R )──
                                仅脱水模式复位

// Network 5: 强度切换逻辑
NETWORK
TITLE = 标准强度到柔和强度
    "Intensity_Edge"            "Standard_Intensity"            "Gentle_Intensity"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    强度切换边沿                 标准强度                        柔和强度
                                "Standard_Intensity"
                                ( R )──
                                标准强度复位

NETWORK
TITLE = 柔和强度到标准强度
    "Intensity_Edge"            "Gentle_Intensity"              "Standard_Intensity"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    强度切换边沿                 柔和强度                        标准强度
                                "Gentle_Intensity"
                                ( R )──
                                柔和强度复位

// Network 6: 系统运行控制
NETWORK
TITLE = 系统启动控制
    "Start_Pause_Edge"          "System_Ready"                  "System_Running"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    启动暂停边沿                 系统准备就绪                    系统运行
                                "System_Paused"
                                ( R )──
                                系统暂停复位

NETWORK
TITLE = 系统暂停控制
    "Start_Pause_Edge"          "System_Running"                "System_Paused"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    启动暂停边沿                 系统运行                        系统暂停
                                "System_Running"
                                ( R )──
                                系统运行复位

NETWORK
TITLE = 系统恢复控制
    "Start_Pause_Edge"          "System_Paused"                 "System_Running"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    启动暂停边沿                 系统暂停                        系统运行
                                "System_Paused"
                                ( R )──
                                系统暂停复位

// Network 7: 进水控制
NETWORK
TITLE = 进水控制逻辑
    "System_Running"            "High_Water_Level"              "Water_Inlet_Valve"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    系统运行                     高水位检测取反                  进水阀

    "Water_Inlet_Valve"         "Water_Filling"
  ──┤ ├─────────────────────────( )──
    进水阀开启                   进水过程

// Network 8: 高水位检测和洗涤启动
NETWORK
TITLE = 高水位检测和洗涤启动
    "High_Water_Level"          "Water_Inlet_Valve"
  ──┤ ├─────────────────────────( R )──
    高水位检测                   进水阀关闭

    "High_Water_Level"          "Full_Program_Mode"             "Wash_Start"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    高水位检测                   全程序模式                      洗涤开始
                                "Water_Filling"
                                ( R )──
                                进水过程复位

    "High_Water_Level"          "Rinse_Spin_Mode"               "Rinse_Start"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    高水位检测                   清洗脱水模式                    清洗开始
                                "Water_Filling"
                                ( R )──
                                进水过程复位

// Network 9: 洗涤过程控制
NETWORK
TITLE = 洗涤过程激活
    "Wash_Start"                "Motor_Enable"                  "Washing"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( )──
    洗涤开始                     电机启动条件                    洗涤过程

// Network 10: 标准强度洗涤定时器
NETWORK
TITLE = 标准强度洗涤定时器
    "Washing"                   "Standard_Intensity"            "Motor_Forward_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    洗涤过程                     标准强度                        电机正转定时器
                                                                PT:=T#4s

    "Motor_Forward_Timer".Q     "Motor_Pause_Timer"
  ──┤ ├─────────────────────────(TON)──
    正转定时器输出               电机暂停定时器
                                PT:=T#1s

    "Motor_Pause_Timer".Q       "Motor_Reverse_Timer"
  ──┤ ├─────────────────────────(TON)──
    暂停定时器输出               电机反转定时器
                                PT:=T#4s

    "Motor_Reverse_Timer".Q     "Motor_Pause2_Timer"
  ──┤ ├─────────────────────────(TON)──
    反转定时器输出               电机暂停定时器2
                                PT:=T#1s

// Network 11: 柔和强度洗涤定时器
NETWORK
TITLE = 柔和强度洗涤定时器
    "Washing"                   "Gentle_Intensity"              "Motor_Forward_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    洗涤过程                     柔和强度                        电机正转定时器
                                                                PT:=T#2s

// Network 12: 电机正转控制
NETWORK
TITLE = 电机正转控制
    "Washing"                   "Motor_Forward_Timer".Q         "Motor_Reverse_Timer".Q         "Motor_Enable"                  "Motor_Forward"
  ──┤ ├─────────────────┤/├─────────────────────┤/├─────────┤ ├─────────────────────────────────( )──
    洗涤过程                     正转定时器未到                  反转定时器未到                  电机启动条件                    电机正转

// Network 13: 电机反转控制
NETWORK
TITLE = 电机反转控制
    "Washing"                   "Motor_Pause_Timer".Q           "Motor_Reverse_Timer".Q         "Motor_Pause2_Timer".Q          "Motor_Enable"                  "Motor_Reverse"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────┤/├─────────────────────┤ ├─────────────────────────────────( )──
    洗涤过程                     暂停定时器输出                  反转定时器未到                  暂停2定时器未到                 电机启动条件                    电机反转

// Network 14: 洗涤循环计数 - 标准强度
NETWORK
TITLE = 洗涤循环计数 - 标准强度
    "Motor_Pause2_Timer".Q      "Standard_Intensity"            "Wash_Cycle_Counter"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(CTU)──
    一个循环完成                 标准强度                        洗涤循环计数器
                                                                PV:=6

    "Motor_Pause2_Timer".Q      "Motor_Forward_Timer"
  ──┤ ├─────────────────────────( R )──
    循环完成                     复位正转定时器

    "Motor_Pause2_Timer".Q      "Motor_Pause_Timer"
  ──┤ ├─────────────────────────( R )──
    循环完成                     复位暂停定时器

    "Motor_Pause2_Timer".Q      "Motor_Reverse_Timer"
  ──┤ ├─────────────────────────( R )──
    循环完成                     复位反转定时器

    "Motor_Pause2_Timer".Q      "Motor_Pause2_Timer"
  ──┤ ├─────────────────────────( R )──
    循环完成                     复位暂停定时器2

// Network 15: 洗涤循环计数 - 柔和强度
NETWORK
TITLE = 洗涤循环计数 - 柔和强度
    "Motor_Pause2_Timer".Q      "Gentle_Intensity"              "Wash_Cycle_Counter"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(CTU)──
    一个循环完成                 柔和强度                        洗涤循环计数器
                                                                PV:=3

// Network 16: 洗涤完成检测 - 标准强度
NETWORK
TITLE = 洗涤完成检测 - 标准强度
    "Wash_Cycle_Counter".Q      "Standard_Intensity"            "Wash_Complete"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    洗涤计数器输出               标准强度                        洗涤完成
                                "Washing"
                                ( R )──
                                洗涤过程复位
                                "Wash_Start"
                                ( R )──
                                洗涤开始复位

    "Wash_Cycle_Counter".Q      "Standard_Intensity"            "Wash_Cycle_Counter"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( R )──
    洗涤计数器输出               标准强度                        洗涤计数器复位

// Network 17: 洗涤完成检测 - 柔和强度
NETWORK
TITLE = 洗涤完成检测 - 柔和强度
    "Wash_Cycle_Counter".Q      "Gentle_Intensity"              "Wash_Complete"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    洗涤计数器输出               柔和强度                        洗涤完成
                                "Washing"
                                ( R )──
                                洗涤过程复位
                                "Wash_Start"
                                ( R )──
                                洗涤开始复位

    "Wash_Cycle_Counter".Q      "Gentle_Intensity"              "Wash_Cycle_Counter"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( R )──
    洗涤计数器输出               柔和强度                        洗涤计数器复位

END_ORGANIZATION_BLOCK
