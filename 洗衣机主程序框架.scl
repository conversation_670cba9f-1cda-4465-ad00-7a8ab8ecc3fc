// S7-1215C 洗衣机控制系统主程序框架
// 采用结构化控制语言(SCL)编写，便于理解和维护

PROGRAM Main_Program
VAR
    // 系统状态变量
    System_State : INT := 0;  // 0=停止, 1=准备, 2=运行, 3=暂停, 4=完成, 5=故障
    Process_Step : INT := 0;  // 当前工艺步骤
    
    // 模式选择变量
    Work_Mode : INT := 1;     // 1=全程序, 2=清洗+脱水, 3=仅脱水
    Intensity_Mode : INT := 1; // 1=标准, 2=柔和
    
    // 按键检测变量
    Start_Pause_Edge : BOOL := FALSE;
    Mode_Switch_Edge : BOOL := FALSE;
    Intensity_Edge : BOOL := FALSE;
    
    // 过程控制变量
    Wash_Cycles_Done : INT := 0;
    Rinse_Cycles_Done : INT := 0;
    Motor_State : INT := 0;   // 0=停止, 1=正转, 2=反转, 3=暂停
    
    // 安全检查变量
    Safety_Check : BOOL := FALSE;
    Error_Code : INT := 0;
END_VAR

BEGIN
    // ========== 系统初始化 ==========
    IF %I0.0 THEN  // 电源开关
        %Q1.5 := TRUE;  // 电源指示灯
        
        // 系统复位
        IF System_State = 0 THEN
            System_State := 1;  // 进入准备状态
            Process_Step := 0;
            Work_Mode := 1;
            Intensity_Mode := 1;
            CALL Reset_All_Outputs;
        END_IF;
    ELSE
        // 电源关闭，系统复位
        System_State := 0;
        CALL Reset_All_Outputs;
        %Q1.5 := FALSE;  // 电源指示灯
    END_IF;
    
    // ========== 安全检查 ==========
    CALL Safety_Check_Function;
    
    // ========== 按键检测与防抖 ==========
    CALL Button_Detection;
    
    // ========== 主状态机 ==========
    CASE System_State OF
        1: // 准备状态
            CALL Prepare_State;
            
        2: // 运行状态
            CALL Running_State;
            
        3: // 暂停状态
            CALL Pause_State;
            
        4: // 完成状态
            CALL Complete_State;
            
        5: // 故障状态
            CALL Error_State;
    END_CASE;
    
    // ========== 指示灯控制 ==========
    CALL LED_Control;
    
    // ========== 输出安全检查 ==========
    CALL Output_Safety_Check;
    
END_PROGRAM;

// ========== 子程序定义 ==========

FUNCTION Reset_All_Outputs : VOID
BEGIN
    %Q0.0 := FALSE;  // 进水阀
    %Q0.1 := FALSE;  // 排水阀
    %Q0.2 := FALSE;  // 电机正转
    %Q0.3 := FALSE;  // 电机反转
    %Q0.4 := FALSE;  // 脱水离合器
    %Q0.5 := FALSE;  // 蜂鸣器
    
    // 复位所有过程状态
    %M1.0 := FALSE;  // 进水过程
    %M1.1 := FALSE;  // 洗涤过程
    %M1.2 := FALSE;  // 排水过程
    %M1.3 := FALSE;  // 清洗过程
    %M1.4 := FALSE;  // 脱水过程
    %M1.5 := FALSE;  // 过程完成
END_FUNCTION;

FUNCTION Safety_Check_Function : VOID
BEGIN
    Safety_Check := TRUE;
    Error_Code := 0;
    
    // 检查门盖是否关闭
    IF NOT %I0.4 AND (System_State = 2) THEN
        Safety_Check := FALSE;
        Error_Code := 1;  // 门盖未关闭
    END_IF;
    
    // 检查水位传感器逻辑
    IF %I0.6 AND NOT %I0.5 THEN
        Safety_Check := FALSE;
        Error_Code := 2;  // 水位传感器故障
    END_IF;
    
    // 如果安全检查失败，进入故障状态
    IF NOT Safety_Check THEN
        System_State := 5;
    END_IF;
END_FUNCTION;

FUNCTION Button_Detection : VOID
VAR_TEMP
    Start_Pause_Current : BOOL;
    Mode_Switch_Current : BOOL;
    Intensity_Current : BOOL;
END_VAR
BEGIN
    // 启动/暂停按键边沿检测
    Start_Pause_Current := %I0.1;
    IF Start_Pause_Current AND NOT Start_Pause_Edge THEN
        // 按键按下
        IF System_State = 1 THEN
            System_State := 2;  // 从准备进入运行
            Process_Step := 1;
        ELSIF System_State = 2 THEN
            System_State := 3;  // 从运行进入暂停
        ELSIF System_State = 3 THEN
            System_State := 2;  // 从暂停恢复运行
        END_IF;
    END_IF;
    Start_Pause_Edge := Start_Pause_Current;
    
    // 模式切换按键（仅在准备状态有效）
    Mode_Switch_Current := %I0.3;
    IF Mode_Switch_Current AND NOT Mode_Switch_Edge AND System_State = 1 THEN
        Work_Mode := Work_Mode + 1;
        IF Work_Mode > 3 THEN
            Work_Mode := 1;
        END_IF;
    END_IF;
    Mode_Switch_Edge := Mode_Switch_Current;
    
    // 强度选择（仅在准备状态有效）
    Intensity_Current := %I0.2;
    IF Intensity_Current AND NOT Intensity_Edge AND System_State = 1 THEN
        IF Intensity_Mode = 1 THEN
            Intensity_Mode := 2;
        ELSE
            Intensity_Mode := 1;
        END_IF;
    END_IF;
    Intensity_Edge := Intensity_Current;
END_FUNCTION;

FUNCTION Prepare_State : VOID
BEGIN
    // 设置模式指示
    %M0.3 := (Work_Mode = 1) OR (Work_Mode = 2);  // 洗涤模式
    %M0.4 := (Work_Mode = 1) OR (Work_Mode = 2);  // 清洗模式
    %M0.5 := TRUE;  // 脱水模式（所有模式都有）
    
    // 设置强度指示
    %M0.6 := (Intensity_Mode = 1);  // 标准强度
    %M0.7 := (Intensity_Mode = 2);  // 柔和强度
    
    // 系统准备就绪
    %M0.0 := TRUE;
    %M0.1 := FALSE;  // 系统未运行
    %M0.2 := FALSE;  // 系统未暂停
END_FUNCTION;
