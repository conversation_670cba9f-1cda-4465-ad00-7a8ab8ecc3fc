// 模块2：功能和程序选择
// S7-1215C 洗衣机控制系统 - 博图V18版本

ORGANIZATION_BLOCK "Module2_Function_Program_Select" [OB2]
TITLE = 功能和程序选择模块
VERSION : 0.1

VAR_TEMP
END_VAR

BEGIN

// Network 1: 功能切换逻辑 - 洗涤到清洗
NETWORK
TITLE = 洗涤功能切换到清洗功能
    "Function_Edge"             "Wash_Function"                 "System_Running"                "Rinse_Function"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( S )──
    功能切换边沿                 当前为洗涤功能                  系统未运行                      清洗功能
                                "Wash_Function"
                                ( R )──
                                洗涤功能复位
                                "Spin_Function"
                                ( R )──
                                脱水功能复位

// Network 2: 功能切换逻辑 - 清洗到脱水
NETWORK
TITLE = 清洗功能切换到脱水功能
    "Function_Edge"             "Rinse_Function"                "System_Running"                "Spin_Function"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( S )──
    功能切换边沿                 当前为清洗功能                  系统未运行                      脱水功能
                                "Rinse_Function"
                                ( R )──
                                清洗功能复位

// Network 3: 功能切换逻辑 - 脱水到洗涤
NETWORK
TITLE = 脱水功能切换到洗涤功能
    "Function_Edge"             "Spin_Function"                 "System_Running"                "Wash_Function"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( S )──
    功能切换边沿                 当前为脱水功能                  系统未运行                      洗涤功能
                                "Spin_Function"
                                ( R )──
                                脱水功能复位

// Network 4: 程序切换逻辑 - 标准到羊毛
NETWORK
TITLE = 标准模式切换到羊毛模式
    "Program_Edge"              "Standard_Mode"                 "System_Running"                "Wool_Mode"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( S )──
    程序切换边沿                 当前为标准模式                  系统未运行                      羊毛模式
                                "Standard_Mode"
                                ( R )──
                                标准模式复位
                                "Heavy_Mode"
                                ( R )──
                                强洗模式复位

// Network 5: 程序切换逻辑 - 羊毛到强洗
NETWORK
TITLE = 羊毛模式切换到强洗模式
    "Program_Edge"              "Wool_Mode"                     "System_Running"                "Heavy_Mode"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( S )──
    程序切换边沿                 当前为羊毛模式                  系统未运行                      强洗模式
                                "Wool_Mode"
                                ( R )──
                                羊毛模式复位

// Network 6: 程序切换逻辑 - 强洗到标准
NETWORK
TITLE = 强洗模式切换到标准模式
    "Program_Edge"              "Heavy_Mode"                    "System_Running"                "Standard_Mode"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( S )──
    程序切换边沿                 当前为强洗模式                  系统未运行                      标准模式
                                "Heavy_Mode"
                                ( R )──
                                强洗模式复位

// Network 7: 水位选择控制 - 高水位
NETWORK
TITLE = 高水位选择控制
    "High_Water_Switch"         "System_Running"                "High_Water_Selected"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    高水位开关                   系统未运行                      高水位选择

    "High_Water_Switch"         "System_Running"                "Low_Water_Selected"
  ──┤ ├─────────────────┤/├─────────────────────────────────( R )──
    高水位开关                   系统未运行                      低水位选择复位

// Network 8: 水位选择控制 - 低水位
NETWORK
TITLE = 低水位选择控制
    "Low_Water_Switch"          "System_Running"                "Low_Water_Selected"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    低水位开关                   系统未运行                      低水位选择

    "Low_Water_Switch"          "System_Running"                "High_Water_Selected"
  ──┤ ├─────────────────┤/├─────────────────────────────────( R )──
    低水位开关                   系统未运行                      高水位选择复位

// Network 9: 默认水位设置
NETWORK
TITLE = 默认水位设置
    "System_Power_On"           "High_Water_Selected"           "Low_Water_Selected"            "High_Water_Selected"
  ──┤ ├─────────────────┤/├─────────────────────┤/├─────────────────────────────────( S )──
    系统通电                     高水位未选择                    低水位未选择                    默认高水位

// Network 10: 门盖状态检测
NETWORK
TITLE = 门盖状态检测
    "Door_Switch"               "Door_Closed_LED"
  ──┤ ├─────────────────────────( )──
    门盖开关                     门盖关闭指示灯

// Network 11: 功能指示灯控制
NETWORK
TITLE = 洗涤功能指示灯
    "Wash_Function"             "Wash_LED"
  ──┤ ├─────────────────────────( )──
    洗涤功能选择                 洗涤指示灯

NETWORK
TITLE = 清洗功能指示灯
    "Rinse_Function"            "Rinse_LED"
  ──┤ ├─────────────────────────( )──
    清洗功能选择                 清洗指示灯

NETWORK
TITLE = 脱水功能指示灯
    "Spin_Function"             "Spin_LED"
  ──┤ ├─────────────────────────( )──
    脱水功能选择                 脱水指示灯

// Network 12: 程序指示灯控制
NETWORK
TITLE = 标准模式指示灯
    "Standard_Mode"             "Standard_LED"
  ──┤ ├─────────────────────────( )──
    标准模式选择                 标准模式指示灯

NETWORK
TITLE = 羊毛模式指示灯
    "Wool_Mode"                 "Wool_LED"
  ──┤ ├─────────────────────────( )──
    羊毛模式选择                 羊毛模式指示灯

NETWORK
TITLE = 强洗模式指示灯
    "Heavy_Mode"                "Heavy_LED"
  ──┤ ├─────────────────────────( )──
    强洗模式选择                 强洗模式指示灯

// Network 13: 水位指示灯控制
NETWORK
TITLE = 高水位指示灯
    "High_Water_Selected"       "High_Water_LED"
  ──┤ ├─────────────────────────( )──
    高水位选择                   高水位指示灯

NETWORK
TITLE = 低水位指示灯
    "Low_Water_Selected"        "Low_Water_LED"
  ──┤ ├─────────────────────────( )──
    低水位选择                   低水位指示灯

END_ORGANIZATION_BLOCK
