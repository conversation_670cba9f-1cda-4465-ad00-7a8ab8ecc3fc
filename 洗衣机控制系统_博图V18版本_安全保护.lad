// S7-1215C 洗衣机控制系统 - 博图V18版本 (安全保护和指示灯控制)
// 最终脱水、安全保护和指示灯控制程序

// Network 32: 最终脱水控制 - 标准强度
NETWORK
TITLE = 最终脱水控制 - 标准强度
    "Final_Spin_Start"          "Standard_Intensity"            "Motor_Enable"                  "Spin_Clutch"
  ──┤ ├─────────────────┤ ├─────────────────────┤ ├─────────────────────────────────( )──
    最终脱水开始                 标准强度                        电机启动条件                    脱水离合器

    "Final_Spin_Start"          "Standard_Intensity"            "Motor_Enable"                  "Motor_Forward"
  ──┤ ├─────────────────┤ ├─────────────────────┤ ├─────────────────────────────────( )──
    最终脱水开始                 标准强度                        电机启动条件                    电机正转(脱水)

    "Final_Spin_Start"          "Standard_Intensity"            "Spin_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    最终脱水开始                 标准强度                        最终脱水定时器
                                                                PT:=T#20s

    "Final_Spin_Start"          "Standard_Intensity"            "Spinning"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( )──
    最终脱水开始                 标准强度                        脱水过程

// Network 33: 最终脱水控制 - 柔和强度
NETWORK
TITLE = 最终脱水控制 - 柔和强度
    "Final_Spin_Start"          "Gentle_Intensity"              "Motor_Enable"                  "Spin_Clutch"
  ──┤ ├─────────────────┤ ├─────────────────────┤ ├─────────────────────────────────( )──
    最终脱水开始                 柔和强度                        电机启动条件                    脱水离合器

    "Final_Spin_Start"          "Gentle_Intensity"              "Motor_Enable"                  "Motor_Forward"
  ──┤ ├─────────────────┤ ├─────────────────────┤ ├─────────────────────────────────( )──
    最终脱水开始                 柔和强度                        电机启动条件                    电机正转(脱水)

    "Final_Spin_Start"          "Gentle_Intensity"              "Spin_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    最终脱水开始                 柔和强度                        最终脱水定时器
                                                                PT:=T#10s

    "Final_Spin_Start"          "Gentle_Intensity"              "Spinning"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( )──
    最终脱水开始                 柔和强度                        脱水过程

// Network 34: 脱水完成检测
NETWORK
TITLE = 脱水完成检测
    "Spin_Timer".Q              "Final_Spin_Complete"
  ──┤ ├─────────────────────────( S )──
    脱水时间到                   脱水完成
                                "Final_Spin_Start"
                                ( R )──
                                脱水开始复位
                                "Spinning"
                                ( R )──
                                脱水过程复位

// Network 35: 脱水完成后停止
NETWORK
TITLE = 脱水完成后停止
    "Final_Spin_Complete"       "Spin_Clutch"
  ──┤ ├─────────────────────────( R )──
    脱水完成                     脱水离合器关闭

    "Final_Spin_Complete"       "Motor_Forward"
  ──┤ ├─────────────────────────( R )──
    脱水完成                     电机停止

    "Final_Spin_Complete"       "Motor_Reverse"
  ──┤ ├─────────────────────────( R )──
    脱水完成                     电机反转停止

    "Final_Spin_Complete"       "Program_Complete"
  ──┤ ├─────────────────────────( S )──
    脱水完成                     程序完成
                                "System_Running"
                                ( R )──
                                系统运行复位
                                "Final_Spin_Complete"
                                ( R )──
                                脱水完成复位

// Network 36: 程序完成后系统复位
NETWORK
TITLE = 程序完成后系统复位
    "Program_Complete"          "System_Ready"
  ──┤ ├─────────────────────────( )──
    程序完成                     系统准备就绪

// Network 37: 紧急停止
NETWORK
TITLE = 紧急停止
    "Emergency_Stop"            "Water_Inlet_Valve"
  ──┤ ├─────────────────────────( R )──
    急停开关                     进水阀关闭

    "Emergency_Stop"            "Water_Drain_Valve"
  ──┤ ├─────────────────────────( S )──
    急停开关                     排水阀开启

    "Emergency_Stop"            "Motor_Forward"
  ──┤ ├─────────────────────────( R )──
    急停开关                     电机正转停止

    "Emergency_Stop"            "Motor_Reverse"
  ──┤ ├─────────────────────────( R )──
    急停开关                     电机反转停止

    "Emergency_Stop"            "Spin_Clutch"
  ──┤ ├─────────────────────────( R )──
    急停开关                     脱水离合器关闭

    "Emergency_Stop"            "Emergency_Stop_Active"
  ──┤ ├─────────────────────────( )──
    急停开关                     紧急停止状态

// Network 38: 门盖安全保护
NETWORK
TITLE = 门盖安全保护
    "Door_Closed"               "Door_Error"
  ──┤/├─────────────────────────( )──
    门盖未关闭                   门盖故障

    "Door_Error"                "Motor_Forward"
  ──┤ ├─────────────────────────( R )──
    门盖故障                     电机正转停止

    "Door_Error"                "Motor_Reverse"
  ──┤ ├─────────────────────────( R )──
    门盖故障                     电机反转停止

    "Door_Error"                "Spin_Clutch"
  ──┤ ├─────────────────────────( R )──
    门盖故障                     脱水离合器关闭

// Network 39: 水位异常保护
NETWORK
TITLE = 水位异常保护
    "High_Water_Level"          "Low_Water_Level"               "Water_Level_Error"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    高水位检测                   低水位检测取反                  水位异常

    "Water_Level_Error"         "Water_Inlet_Valve"
  ──┤ ├─────────────────────────( R )──
    水位异常                     进水阀关闭

    "Water_Level_Error"         "Water_Drain_Valve"
  ──┤ ├─────────────────────────( S )──
    水位异常                     排水阀开启

// Network 40: 系统故障综合
NETWORK
TITLE = 系统故障综合
    "Door_Error"                "Water_Level_Error"             "Emergency_Stop_Active"         "System_Error"
  ──┤ ├─────────────────┤ ├─────────────────────┤ ├─────────────────────────────────( )──
    门盖故障                     水位异常                        紧急停止                        系统故障

    "System_Error"              "Error_LED"
  ──┤ ├─────────────────────────( )──
    系统故障                     故障指示灯

    "System_Error"              "System_Running"
  ──┤ ├─────────────────────────( R )──
    系统故障                     系统运行停止

// Network 41: 强度指示灯控制
NETWORK
TITLE = 标准强度指示灯
    "Standard_Intensity"        "Standard_LED"
  ──┤ ├─────────────────────────( )──
    标准强度选择                 标准强度指示灯

NETWORK
TITLE = 柔和强度指示灯
    "Gentle_Intensity"          "Gentle_LED"
  ──┤ ├─────────────────────────( )──
    柔和强度选择                 柔和强度指示灯

// Network 42: 洗涤指示灯控制
NETWORK
TITLE = 洗涤指示灯常亮控制
    "Full_Program_Mode"         "Washing"                       "Wash_LED"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    全程序模式                   洗涤未进行                      洗涤指示灯常亮

NETWORK
TITLE = 洗涤指示灯闪烁控制
    "Washing"                   "LED_Flash_Timer"
  ──┤ ├─────────────────────────(TON)──
    洗涤过程                     闪烁定时器
                                PT:=T#500ms

    "LED_Flash_Timer".Q         "LED_Flash_Timer"
  ──┤ ├─────────────────────────( R )──
    闪烁定时器输出               闪烁定时器复位

    "Washing"                   "LED_Flash_Timer".Q             "Wash_LED"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( )──
    洗涤过程                     闪烁信号                        洗涤指示灯闪烁

NETWORK
TITLE = 洗涤指示灯熄灭控制
    "Wash_Complete"             "Wash_LED"
  ──┤ ├─────────────────────────( R )──
    洗涤完成                     洗涤指示灯熄灭

// Network 43: 清洗指示灯控制
NETWORK
TITLE = 清洗指示灯常亮控制
    "Full_Program_Mode"         "Wash_Complete"                 "Rinsing"                       "Rinse_LED"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( )──
    全程序模式                   洗涤完成                        清洗未进行                      清洗指示灯常亮

    "Rinse_Spin_Mode"           "Rinsing"                       "Rinse_LED"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    清洗脱水模式                 清洗未进行                      清洗指示灯常亮

NETWORK
TITLE = 清洗指示灯闪烁控制
    "Rinsing"                   "LED_Flash_Timer".Q             "Rinse_LED"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( )──
    清洗过程                     闪烁信号                        清洗指示灯闪烁

NETWORK
TITLE = 清洗指示灯熄灭控制
    "Rinse_Complete"            "Rinse_LED"
  ──┤ ├─────────────────────────( R )──
    清洗完成                     清洗指示灯熄灭

// Network 44: 脱水指示灯控制
NETWORK
TITLE = 脱水指示灯常亮控制
    "Full_Program_Mode"         "Rinse_Complete"                "Spinning"                      "Spin_LED"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( )──
    全程序模式                   清洗完成                        脱水未进行                      脱水指示灯常亮

    "Rinse_Spin_Mode"           "Rinse_Complete"                "Spinning"                      "Spin_LED"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( )──
    清洗脱水模式                 清洗完成                        脱水未进行                      脱水指示灯常亮

    "Spin_Only_Mode"            "System_Running"                "Spinning"                      "Spin_LED"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( )──
    仅脱水模式                   系统运行                        脱水未进行                      脱水指示灯常亮

NETWORK
TITLE = 脱水指示灯闪烁控制
    "Spinning"                  "LED_Flash_Timer".Q             "Spin_LED"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( )──
    脱水过程                     闪烁信号                        脱水指示灯闪烁

NETWORK
TITLE = 脱水指示灯熄灭控制
    "Final_Spin_Complete"       "Spin_LED"
  ──┤ ├─────────────────────────( R )──
    脱水完成                     脱水指示灯熄灭

// Network 45: 电源关闭时所有输出复位
NETWORK
TITLE = 电源关闭时所有输出复位
    "Power_Switch"              "Water_Inlet_Valve"
  ──┤/├─────────────────────────( R )──
    电源关闭                     进水阀关闭

    "Power_Switch"              "Water_Drain_Valve"
  ──┤/├─────────────────────────( R )──
    电源关闭                     排水阀关闭

    "Power_Switch"              "Motor_Forward"
  ──┤/├─────────────────────────( R )──
    电源关闭                     电机正转停止

    "Power_Switch"              "Motor_Reverse"
  ──┤/├─────────────────────────( R )──
    电源关闭                     电机反转停止

    "Power_Switch"              "Spin_Clutch"
  ──┤/├─────────────────────────( R )──
    电源关闭                     脱水离合器关闭

    "Power_Switch"              "Wash_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     洗涤指示灯熄灭

    "Power_Switch"              "Rinse_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     清洗指示灯熄灭

    "Power_Switch"              "Spin_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     脱水指示灯熄灭

    "Power_Switch"              "Standard_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     标准强度指示灯熄灭

    "Power_Switch"              "Gentle_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     柔和强度指示灯熄灭

    "Power_Switch"              "Error_LED"
  ──┤/├─────────────────────────( R )──
    电源关闭                     故障指示灯熄灭
