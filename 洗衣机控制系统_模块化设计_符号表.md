# 洗衣机控制系统 - 模块化设计符号表 (博图V18)

## 输入信号 (Input Signals)

| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| Power_Switch | %I0.0 | BOOL | 电源开关 | 按下通电 |
| Function_Button | %I0.1 | BOOL | 功能按钮 | 切换洗涤/清洗/脱水 |
| Program_Button | %I0.2 | BOOL | 程序按钮 | 切换标准/羊毛/强洗 |
| Start_Stop_Button | %I0.3 | BOOL | 启停按钮 | 启动/停止洗衣程序 |
| Door_Switch | %I0.4 | BOOL | 门盖开关 | 门盖状态检测 |
| High_Water_Switch | %I0.5 | BOOL | 高水位开关 | 选择高水位 |
| Low_Water_Switch | %I0.6 | BOOL | 低水位开关 | 选择低水位 |

## 输出信号 (Output Signals)

### 执行器输出
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| Water_Inlet_Valve | %Q0.0 | BOOL | 进水阀 | 1=进水 |
| Water_Drain_Valve | %Q0.1 | BOOL | 排水阀 | 1=排水 |
| Motor_Forward | %Q0.2 | BOOL | 电机正转 | 1=正转 |
| Motor_Reverse | %Q0.3 | BOOL | 电机反转 | 1=反转 |

### 功能指示灯
| 符号名称 | 地址 | 数据类型 | 中文描述 | 状态说明 |
|----------|------|----------|----------|----------|
| Wash_LED | %Q1.0 | BOOL | 洗涤指示灯 | 洗涤功能选择/进行时亮 |
| Rinse_LED | %Q1.1 | BOOL | 清洗指示灯 | 清洗功能选择/进行时亮 |
| Spin_LED | %Q1.2 | BOOL | 脱水指示灯 | 脱水功能选择/进行时亮 |

### 程序指示灯
| 符号名称 | 地址 | 数据类型 | 中文描述 | 状态说明 |
|----------|------|----------|----------|----------|
| Standard_LED | %Q1.3 | BOOL | 标准模式指示灯 | 标准模式选择时亮 |
| Wool_LED | %Q1.4 | BOOL | 羊毛模式指示灯 | 羊毛模式选择时亮 |
| Heavy_LED | %Q1.5 | BOOL | 强洗模式指示灯 | 强洗模式选择时亮 |

### 状态指示灯
| 符号名称 | 地址 | 数据类型 | 中文描述 | 状态说明 |
|----------|------|----------|----------|----------|
| Door_Closed_LED | %Q2.0 | BOOL | 门盖关闭指示灯 | 门盖关好时亮 |
| High_Water_LED | %Q2.1 | BOOL | 高水位指示灯 | 选择高水位时亮 |
| Low_Water_LED | %Q2.2 | BOOL | 低水位指示灯 | 选择低水位时亮 |
| Water_Inlet_LED | %Q2.3 | BOOL | 进水指示灯 | 进水时亮 |
| Water_Drain_LED | %Q2.4 | BOOL | 排水指示灯 | 排水时亮 |
| Motor_Forward_LED | %Q2.5 | BOOL | 正转指示灯 | 电机正转时亮 |
| Motor_Reverse_LED | %Q2.6 | BOOL | 反转指示灯 | 电机反转时亮 |

## 内部变量 (Memory Variables)

### 系统状态变量
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| System_Power_On | %M0.0 | BOOL | 系统通电 | 电源开关状态 |
| System_Running | %M0.1 | BOOL | 系统运行 | 洗衣程序运行中 |
| System_Stopped | %M0.2 | BOOL | 系统停止 | 洗衣程序停止 |

### 功能选择变量
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| Wash_Function | %M1.0 | BOOL | 洗涤功能 | 选择洗涤功能 |
| Rinse_Function | %M1.1 | BOOL | 清洗功能 | 选择清洗功能 |
| Spin_Function | %M1.2 | BOOL | 脱水功能 | 选择脱水功能 |

### 程序选择变量
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| Standard_Mode | %M2.0 | BOOL | 标准模式 | 标准洗涤模式 |
| Wool_Mode | %M2.1 | BOOL | 羊毛模式 | 羊毛洗涤模式 |
| Heavy_Mode | %M2.2 | BOOL | 强洗模式 | 强力洗涤模式 |

### 水位选择变量
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| High_Water_Selected | %M3.0 | BOOL | 高水位选择 | 选择高水位 |
| Low_Water_Selected | %M3.1 | BOOL | 低水位选择 | 选择低水位 |

### 按键边沿检测变量
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| Function_Edge | %M4.0 | BOOL | 功能按钮边沿 | 功能切换触发 |
| Program_Edge | %M4.1 | BOOL | 程序按钮边沿 | 程序切换触发 |
| Start_Stop_Edge | %M4.2 | BOOL | 启停按钮边沿 | 启停触发 |
| Function_Button_Last | %M4.3 | BOOL | 功能按钮上次状态 | 边沿检测用 |
| Program_Button_Last | %M4.4 | BOOL | 程序按钮上次状态 | 边沿检测用 |
| Start_Stop_Last | %M4.5 | BOOL | 启停按钮上次状态 | 边沿检测用 |

### 过程状态变量
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| Water_Fill_Complete | %M5.0 | BOOL | 进水完成 | 进水阶段完成 |
| Wash_Process_Active | %M5.1 | BOOL | 洗涤过程激活 | 洗涤程序进行中 |
| Rinse_Process_Active | %M5.2 | BOOL | 清洗过程激活 | 清洗程序进行中 |
| Spin_Process_Active | %M5.3 | BOOL | 脱水过程激活 | 脱水程序进行中 |
| Wash_Complete | %M5.4 | BOOL | 洗涤完成 | 洗涤阶段完成 |
| Rinse_Complete | %M5.5 | BOOL | 清洗完成 | 清洗阶段完成 |
| Spin_Complete | %M5.6 | BOOL | 脱水完成 | 脱水阶段完成 |
| Program_Complete | %M5.7 | BOOL | 程序完成 | 整个程序完成 |

### 电机控制变量
| 符号名称 | 地址 | 数据类型 | 中文描述 | 备注 |
|----------|------|----------|----------|------|
| Motor_Forward_Active | %M6.0 | BOOL | 电机正转激活 | 正转状态 |
| Motor_Reverse_Active | %M6.1 | BOOL | 电机反转激活 | 反转状态 |
| Motor_Pause_Active | %M6.2 | BOOL | 电机暂停激活 | 暂停状态 |

## 定时器变量 (Timer Variables)

### 进水排水定时器
| 符号名称 | 地址 | 数据类型 | 中文描述 | 时间设定 |
|----------|------|----------|----------|----------|
| Water_Inlet_Timer | %T1 | TON | 进水定时器 | 高水位1min/低水位30s |
| Water_Drain_Timer | %T2 | TON | 排水定时器 | 高水位1min/低水位30s |

### 电机控制定时器
| 符号名称 | 地址 | 数据类型 | 中文描述 | 时间设定 |
|----------|------|----------|----------|----------|
| Motor_Forward_Timer | %T3 | TON | 电机正转定时器 | 标准4s/羊毛2s/强洗9s |
| Motor_Pause_Timer | %T4 | TON | 电机暂停定时器 | 1s |
| Motor_Reverse_Timer | %T5 | TON | 电机反转定时器 | 标准4s/羊毛2s/强洗9s |
| Motor_Pause2_Timer | %T6 | TON | 电机暂停定时器2 | 1s |

### 程序总时间定时器
| 符号名称 | 地址 | 数据类型 | 中文描述 | 时间设定 |
|----------|------|----------|----------|----------|
| Wash_Program_Timer | %T10 | TON | 洗涤程序定时器 | 标准15min/羊毛10min/强洗25min |
| Rinse_Program_Timer | %T11 | TON | 清洗程序定时器 | 标准15min/羊毛10min/强洗25min |
| Spin_Program_Timer | %T12 | TON | 脱水程序定时器 | 10min |

## 模块化设计说明

### 模块1：系统初始化和按键处理
- 电源控制
- 按键边沿检测
- 默认设置

### 模块2：功能和程序选择
- 功能切换逻辑（洗涤/清洗/脱水）
- 程序切换逻辑（标准/羊毛/强洗）
- 水位选择逻辑

### 模块3：安全检查和启动控制
- 门盖状态检测
- 启动条件检查
- 系统运行控制

### 模块4：进水排水控制
- 进水阀控制
- 排水阀控制
- 水位时间控制

### 模块5：电机控制
- 电机正转控制
- 电机反转控制
- 电机暂停控制
- 不同模式的时间参数

### 模块6：程序流程控制
- 洗涤程序控制
- 清洗程序控制
- 脱水程序控制
- 程序切换逻辑

### 模块7：指示灯控制
- 功能指示灯
- 程序指示灯
- 状态指示灯
- 电机状态指示灯
