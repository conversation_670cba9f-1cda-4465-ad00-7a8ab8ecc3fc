// S7-1215C 洗衣机控制系统 - 博图V18版本 (续)
// 清洗、脱水和安全保护程序

// Network 18: 洗涤完成后排水
NETWORK
TITLE = 洗涤完成后排水
    "Wash_Complete"             "Water_Drain_Valve"
  ──┤ ├─────────────────────────( S )──
    洗涤完成                     排水阀开启
                                "Water_Inlet_Valve"
                                ( R )──
                                进水阀关闭

    "Wash_Complete"             "Water_Draining"
  ──┤ ├─────────────────────────( )──
    洗涤完成                     排水过程

// Network 19: 洗涤后中间脱水
NETWORK
TITLE = 洗涤后中间脱水
    "Water_Draining"            "Low_Water_Level"               "Drain_Delay_Timer"
  ──┤ ├─────────────────┤/├─────────────────────────────────(TON)──
    排水过程                     低水位检测取反                  延时5秒
                                                                PT:=T#5s

    "Drain_Delay_Timer".Q       "Intermediate_Spin"
  ──┤ ├─────────────────────────( S )──
    延时到                       中间脱水
                                "Water_Draining"
                                ( R )──
                                排水过程复位

    "Intermediate_Spin"         "Motor_Enable"                  "Spin_Clutch"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( )──
    中间脱水                     电机启动条件                    脱水离合器

    "Intermediate_Spin"         "Motor_Enable"                  "Motor_Forward"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( )──
    中间脱水                     电机启动条件                    电机正转(脱水)

    "Intermediate_Spin"         "Spin_Timer"
  ──┤ ├─────────────────────────(TON)──
    中间脱水                     脱水定时器
                                PT:=T#10s

    "Spin_Timer".Q              "Spin_Clutch"
  ──┤ ├─────────────────────────( R )──
    脱水完成                     脱水离合器关闭

    "Spin_Timer".Q              "Motor_Forward"
  ──┤ ├─────────────────────────( R )──
    脱水完成                     电机停止

    "Spin_Timer".Q              "Rinse_Start"
  ──┤ ├─────────────────────────( S )──
    脱水完成                     清洗开始
                                "Intermediate_Spin"
                                ( R )──
                                中间脱水复位
                                "Wash_Complete"
                                ( R )──
                                洗涤完成复位

// Network 20: 清洗进水控制
NETWORK
TITLE = 清洗进水控制
    "Rinse_Start"               "Water_Drain_Valve"
  ──┤ ├─────────────────────────( R )──
    清洗开始                     排水阀关闭

    "Rinse_Start"               "High_Water_Level"              "Water_Inlet_Valve"
  ──┤ ├─────────────────┤/├─────────────────────────────────( )──
    清洗开始                     高水位检测取反                  进水阀开启

    "Water_Inlet_Valve"         "Water_Filling"
  ──┤ ├─────────────────────────( )──
    进水阀开启                   进水过程

// Network 21: 清洗进水完成
NETWORK
TITLE = 清洗进水完成
    "High_Water_Level"          "Water_Inlet_Valve"
  ──┤ ├─────────────────────────( R )──
    高水位检测                   进水阀关闭

    "High_Water_Level"          "Rinse_Start"                   "Rinsing"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    高水位检测                   清洗开始                        清洗过程激活
                                "Water_Filling"
                                ( R )──
                                进水过程复位

// Network 22: 标准强度清洗定时器
NETWORK
TITLE = 标准强度清洗定时器
    "Rinsing"                   "Standard_Intensity"            "Motor_Forward_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    清洗过程                     标准强度                        电机正转定时器
                                                                PT:=T#4s

    "Motor_Forward_Timer".Q     "Motor_Pause_Timer"
  ──┤ ├─────────────────────────(TON)──
    正转定时器输出               电机暂停定时器
                                PT:=T#1s

    "Motor_Pause_Timer".Q       "Motor_Reverse_Timer"
  ──┤ ├─────────────────────────(TON)──
    暂停定时器输出               电机反转定时器
                                PT:=T#4s

    "Motor_Reverse_Timer".Q     "Motor_Pause2_Timer"
  ──┤ ├─────────────────────────(TON)──
    反转定时器输出               电机暂停定时器2
                                PT:=T#1s

// Network 23: 柔和强度清洗定时器
NETWORK
TITLE = 柔和强度清洗定时器
    "Rinsing"                   "Gentle_Intensity"              "Motor_Forward_Timer"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(TON)──
    清洗过程                     柔和强度                        电机正转定时器
                                                                PT:=T#2s

// Network 24: 清洗电机控制
NETWORK
TITLE = 清洗电机正转控制
    "Rinsing"                   "Motor_Forward_Timer".Q         "Motor_Reverse_Timer".Q         "Motor_Enable"                  "Motor_Forward"
  ──┤ ├─────────────────┤/├─────────────────────┤/├─────────┤ ├─────────────────────────────────( )──
    清洗过程                     正转定时器未到                  反转定时器未到                  电机启动条件                    电机正转

NETWORK
TITLE = 清洗电机反转控制
    "Rinsing"                   "Motor_Pause_Timer".Q           "Motor_Reverse_Timer".Q         "Motor_Pause2_Timer".Q          "Motor_Enable"                  "Motor_Reverse"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────┤/├─────────────────────┤ ├─────────────────────────────────( )──
    清洗过程                     暂停定时器输出                  反转定时器未到                  暂停2定时器未到                 电机启动条件                    电机反转

// Network 25: 清洗循环计数 - 标准强度
NETWORK
TITLE = 清洗循环计数 - 标准强度
    "Motor_Pause2_Timer".Q      "Standard_Intensity"            "Rinse_Cycle_Counter"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(CTU)──
    一个循环完成                 标准强度                        清洗循环计数器
                                                                PV:=3

    "Motor_Pause2_Timer".Q      "Motor_Forward_Timer"
  ──┤ ├─────────────────────────( R )──
    循环完成                     复位定时器组

    "Motor_Pause2_Timer".Q      "Motor_Pause_Timer"
  ──┤ ├─────────────────────────( R )──

    "Motor_Pause2_Timer".Q      "Motor_Reverse_Timer"
  ──┤ ├─────────────────────────( R )──

    "Motor_Pause2_Timer".Q      "Motor_Pause2_Timer"
  ──┤ ├─────────────────────────( R )──

// Network 26: 清洗循环计数 - 柔和强度
NETWORK
TITLE = 清洗循环计数 - 柔和强度
    "Motor_Pause2_Timer".Q      "Gentle_Intensity"              "Rinse_Cycle_Counter"
  ──┤ ├─────────────────┤ ├─────────────────────────────────(CTU)──
    一个循环完成                 柔和强度                        清洗循环计数器
                                                                PV:=2

// Network 27: 清洗完成检测 - 标准强度
NETWORK
TITLE = 清洗完成检测 - 标准强度
    "Rinse_Cycle_Counter".Q     "Standard_Intensity"            "Rinse_Complete"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    清洗计数器输出               标准强度                        清洗完成
                                "Rinsing"
                                ( R )──
                                清洗过程复位
                                "Rinse_Start"
                                ( R )──
                                清洗开始复位

    "Rinse_Cycle_Counter".Q     "Standard_Intensity"            "Rinse_Cycle_Counter"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( R )──
    清洗计数器输出               标准强度                        清洗计数器复位

// Network 28: 清洗完成检测 - 柔和强度
NETWORK
TITLE = 清洗完成检测 - 柔和强度
    "Rinse_Cycle_Counter".Q     "Gentle_Intensity"              "Rinse_Complete"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( S )──
    清洗计数器输出               柔和强度                        清洗完成
                                "Rinsing"
                                ( R )──
                                清洗过程复位
                                "Rinse_Start"
                                ( R )──
                                清洗开始复位

    "Rinse_Cycle_Counter".Q     "Gentle_Intensity"              "Rinse_Cycle_Counter"
  ──┤ ├─────────────────┤ ├─────────────────────────────────( R )──
    清洗计数器输出               柔和强度                        清洗计数器复位

// Network 29: 清洗完成后排水
NETWORK
TITLE = 清洗完成后排水
    "Rinse_Complete"            "Water_Drain_Valve"
  ──┤ ├─────────────────────────( S )──
    清洗完成                     排水阀开启
                                "Water_Inlet_Valve"
                                ( R )──
                                进水阀关闭

    "Rinse_Complete"            "Water_Draining"
  ──┤ ├─────────────────────────( )──
    清洗完成                     排水过程

// Network 30: 最终脱水准备
NETWORK
TITLE = 最终脱水准备
    "Water_Draining"            "Low_Water_Level"               "Drain_Delay_Timer"
  ──┤ ├─────────────────┤/├─────────────────────────────────(TON)──
    排水过程                     低水位检测取反                  延时5秒
                                                                PT:=T#5s

    "Drain_Delay_Timer".Q       "Final_Spin_Start"
  ──┤ ├─────────────────────────( S )──
    延时到                       最终脱水开始
                                "Water_Draining"
                                ( R )──
                                排水过程复位
                                "Rinse_Complete"
                                ( R )──
                                清洗完成复位

// Network 31: 仅脱水模式直接启动
NETWORK
TITLE = 仅脱水模式直接启动
    "System_Running"            "Spin_Only_Mode"                "Low_Water_Level"               "Final_Spin_Start"
  ──┤ ├─────────────────┤ ├─────────────────────┤/├─────────────────────────────────( )──
    系统运行                     仅脱水模式                      低水位检测取反                  最终脱水开始
